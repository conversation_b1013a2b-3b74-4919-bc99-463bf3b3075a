<template>
  <el-icon @click="handleChange" style="margin-right: 10px; cursor: pointer">
    <component :is="settingStore.fold ? 'Expand' : 'Fold'" />
  </el-icon>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item
      v-for="item in route.matched"
      :key="item.path"
      v-show="item.meta.title"
      :to="item.path"
    >
      <!-- <el-icon>
				<component :is="item.meta.icon"></component>
			</el-icon> -->
      <span>{{ item.meta.title }}</span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>
<script lang="ts" setup>
import { useRoute } from "vue-router";
import useSettingStore from "@/store/model/setting";
const route = useRoute();
let settingStore = useSettingStore();
let handleChange = () => {
  settingStore.fold = !settingStore.fold;
};
</script>
<style lang="scss" scoped></style>
