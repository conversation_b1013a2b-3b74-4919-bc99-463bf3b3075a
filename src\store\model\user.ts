import { defineStore } from "pinia";
// 引入路由，存入
import router from "@/router";
import { reqLogin, reqUserInfo, reqLogout } from "@/api/user/user";
import { ElMessage } from "element-plus";

import { constantRoutes, asyncRoutes, anyRouter } from "@/router/routers";
import { setToken, removeToken, getToken } from "@/utils/storage";
import cloneDeep from "lodash/cloneDeep";
// 过滤异步路由
const filterAsyncRoute = (asyncRoutes: any, routes: any) => {
  return asyncRoutes.filter((route: any) => {
    if (routes.indexOf(route.name) !== -1) {
      if (route.children) {
        route.children = filterAsyncRoute(route.children, routes);
      }
      return true;
    }
    return false;
  });
};
let useUserStore = defineStore("user", {
  state: () => ({
    token: getToken() || "",
    username: "",
    avatar: "",
    menuRouters: constantRoutes, // 菜单路由
    buttons: [],
    routes: [], //
  }),
  actions: {
    async userLogin(params: any) {
      // 登录逻辑
      let loginInfo: any = await reqLogin(params);
      if (loginInfo.code == 200) {
        setToken(loginInfo.data);
        return "ok";
      } else {
        ElMessage.error(loginInfo.data);
        return Promise.reject(new Error(loginInfo.message));
      }
    },
    async getRouter() {
      let userAsyncRoute = filterAsyncRoute(
        cloneDeep(asyncRoutes),
        this.routes
      );
      this.menuRouters = [...constantRoutes, ...userAsyncRoute, ...anyRouter];
      let asyncRouterList = [...userAsyncRoute, ...anyRouter];
      asyncRouterList.forEach((item: any) => {
        router.addRoute(item);
      });
    },
    async getUserInfo() {
      // 获取用户信息
      let userInfo: any = await reqUserInfo();
      if (userInfo.code == 200) {
        this.username = userInfo.data.name;
        this.avatar = userInfo.data.avatar;
        this.buttons = userInfo.data.buttons;
        this.routes = userInfo.data.routes;
        await this.getRouter();
        //计算当前用户需要展示的异步路由
        return "ok";
      } else {
        ElMessage.error(userInfo.data);
        return Promise.reject(new Error(userInfo.data));
      }
    },
    logout() {
      // 退出登录
      removeToken();
      this.token = "";
      this.username = "";
      this.avatar = "";
    },
  },
});

export default useUserStore;
